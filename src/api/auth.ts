import { apiClient, ApiResponse } from './client';
import { mockAuthApi } from './mockAuth';
import { USE_MOCK_API, API_ENDPOINTS, TOKEN_STORAGE_KEY, REFRESH_TOKEN_STORAGE_KEY, USER_STORAGE_KEY } from './config';

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'PATIENT' | 'PROVIDER' | 'ADMIN';
  avatar?: string;
  phone?: string;
  dateOfBirth?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: 'PATIENT' | 'PROVIDER';
  phone?: string;
  dateOfBirth?: string;
  address?: string;
  additionalInfo?: any;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface ResetPasswordData {
  email: string;
}

export interface ConfirmResetPasswordData {
  token: string;
  newPassword: string;
}

// Mock API flag is now imported from config

// Auth API
export const authApi = {
  // Login
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    if (USE_MOCK_API) {
      return mockAuthApi.login(credentials);
    }
    return apiClient.post<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials);
  },

  // Register
  async register(data: RegisterData): Promise<ApiResponse<AuthResponse>> {
    if (USE_MOCK_API) {
      return mockAuthApi.register(data);
    }
    return apiClient.post<AuthResponse>(API_ENDPOINTS.AUTH.REGISTER, data);
  },

  // Logout
  async logout(): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAuthApi.logout();
    }
    return apiClient.post<void>(API_ENDPOINTS.AUTH.LOGOUT);
  },

  // Get current user profile
  async getProfile(): Promise<ApiResponse<User>> {
    if (USE_MOCK_API) {
      return mockAuthApi.getProfile();
    }
    return apiClient.get<User>(API_ENDPOINTS.AUTH.PROFILE);
  },

  // Update profile
  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    if (USE_MOCK_API) {
      return mockAuthApi.updateProfile(data);
    }
    return apiClient.put<User>(API_ENDPOINTS.AUTH.PROFILE, data);
  },

  // Change password
  async changePassword(data: ChangePasswordData): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAuthApi.changePassword(data);
    }
    return apiClient.put<void>(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, data);
  },

  // Request password reset
  async resetPassword(data: ResetPasswordData): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAuthApi.resetPassword(data);
    }
    return apiClient.post<void>(API_ENDPOINTS.AUTH.RESET_PASSWORD, data);
  },

  // Confirm password reset
  async confirmResetPassword(data: ConfirmResetPasswordData): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAuthApi.confirmResetPassword(data);
    }
    return apiClient.post<void>(API_ENDPOINTS.AUTH.CONFIRM_RESET_PASSWORD, data);
  },

  // Refresh token
  async refreshToken(): Promise<ApiResponse<{ token: string; refreshToken?: string }>> {
    if (USE_MOCK_API) {
      return mockAuthApi.refreshToken();
    }
    const refreshToken = localStorage.getItem(REFRESH_TOKEN_STORAGE_KEY);
    return apiClient.post<{ token: string; refreshToken?: string }>(API_ENDPOINTS.AUTH.REFRESH_TOKEN, {
      refreshToken
    });
  },

  // Verify email
  async verifyEmail(token: string): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAuthApi.verifyEmail(token);
    }
    return apiClient.post<void>(API_ENDPOINTS.AUTH.VERIFY_EMAIL, { token });
  },

  // Resend verification email
  async resendVerification(): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      return mockAuthApi.resendVerification();
    }
    return apiClient.post<void>(API_ENDPOINTS.AUTH.RESEND_VERIFICATION);
  },

  // Verify token
  async verifyToken(): Promise<ApiResponse<{ valid: boolean; user?: User }>> {
    if (USE_MOCK_API) {
      return mockAuthApi.verifyToken();
    }
    return apiClient.get<{ valid: boolean; user?: User }>(API_ENDPOINTS.AUTH.VERIFY_TOKEN);
  },
};

export default authApi;
