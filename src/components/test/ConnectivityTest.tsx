import React, { useState, useEffect } from 'react';
import { apiClient } from '../../api/client';
import { API_BASE_URL } from '../../api/config';

interface ConnectivityTestProps {
  onClose?: () => void;
}

export const ConnectivityTest: React.FC<ConnectivityTestProps> = ({ onClose }) => {
  const [status, setStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testConnectivity = async () => {
    setStatus('testing');
    setError(null);
    setResult(null);

    try {
      // Test basic connectivity to backend
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
      const data = await response.json();
      
      setResult(data);
      setStatus('success');
    } catch (err: any) {
      setError(err.message || 'Connection failed');
      setStatus('error');
    }
  };

  const testApiClient = async () => {
    setStatus('testing');
    setError(null);
    setResult(null);

    try {
      // Test API client
      const response = await apiClient.get('/test');
      setResult(response);
      setStatus('success');
    } catch (err: any) {
      setError(err.message || 'API client test failed');
      setStatus('error');
    }
  };

  useEffect(() => {
    // Auto-test on mount
    testConnectivity();
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Backend Connectivity Test</h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          )}
        </div>

        <div className="space-y-4">
          <div>
            <p className="text-sm text-gray-600 mb-2">
              Backend URL: <code className="bg-gray-100 px-1 rounded">{API_BASE_URL}</code>
            </p>
          </div>

          <div className="space-y-2">
            <button
              onClick={testConnectivity}
              disabled={status === 'testing'}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {status === 'testing' ? 'Testing Health Endpoint...' : 'Test Health Endpoint'}
            </button>

            <button
              onClick={testApiClient}
              disabled={status === 'testing'}
              className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              {status === 'testing' ? 'Testing API Client...' : 'Test API Client'}
            </button>
          </div>

          {status === 'success' && result && (
            <div className="p-3 bg-green-50 border border-green-200 rounded">
              <p className="text-green-800 font-medium mb-2">✅ Connection Successful!</p>
              <pre className="text-xs text-green-700 overflow-auto max-h-32">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}

          {status === 'error' && error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded">
              <p className="text-red-800 font-medium mb-2">❌ Connection Failed</p>
              <p className="text-red-700 text-sm">{error}</p>
              <p className="text-red-600 text-xs mt-2">
                Make sure the backend server is running on port 3000
              </p>
            </div>
          )}

          {status === 'testing' && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded">
              <p className="text-blue-800 font-medium">🔄 Testing connection...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConnectivityTest;
